use regex::Regex;
use reqwest;
use scraper::{Html, Selector};
use serde_json::{json, Value};
use std::time::Duration;

use crate::config::USER_AGENT;
use crate::crypto::get_weapi_params;
use crate::{print_info, print_success, print_warn};

// ==================== URL解析模块 ====================
pub async fn parse_and_route_url(raw_url: &str) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    let cleaned_url = raw_url.trim().split('&').next().unwrap_or(raw_url);
    print_info!("正在解析URL: {}", cleaned_url);

    let client = reqwest::Client::builder()
        .timeout(Duration::from_secs(30))
        .user_agent(USER_AGENT)
        .build()?;

    // 匹配歌单或专辑
    let playlist_album_re =
        Regex::new(r"music\.163\.com/(?:#/)?(album|playlist)\?id=(\d+)").unwrap();
    if let Some(captures) = playlist_album_re.captures(cleaned_url) {
        let url_type = captures.get(1).unwrap().as_str();
        let item_id = captures.get(2).unwrap().as_str();
        return match url_type {
            "playlist" => fetch_playlist_data(item_id, &client).await,
            "album" => fetch_album_data(item_id, &client).await,
            _ => Err("未知的URL类型".into()),
        };
    }

    // 匹配单曲
    let song_re = Regex::new(r"music\.163\.com/(?:#/)?song\?id=(\d+)").unwrap();
    if let Some(captures) = song_re.captures(cleaned_url) {
        let song_id = captures.get(1).unwrap().as_str();
        print_info!("✓ 识别为单曲URL");
        return Ok(vec![format!("https://music.163.com/song?id={}", song_id)]);
    }

    Err("URL格式不匹配任何有效的单曲、专辑或歌单".into())
}

async fn fetch_playlist_data(
    playlist_id: &str,
    client: &reqwest::Client,
) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    print_info!("✓ 识别为歌单，正在获取歌曲列表...");

    let playlist_api_url = "https://music.163.com/weapi/v6/playlist/detail?csrf_token=";
    let playlist_payload = json!({
        "id": playlist_id,
        "n": 100000,
        "s": 8,
        "csrf_token": ""
    });

    let encrypted_playlist_data = get_weapi_params(&playlist_payload)?;

    let response = client
        .post(playlist_api_url)
        .header("User-Agent", USER_AGENT)
        .header("Referer", "https://music.163.com/")
        .header("Content-Type", "application/x-www-form-urlencoded")
        .header("Accept-Language", "zh-CN,zh;q=0.9")
        .form(&encrypted_playlist_data)
        .timeout(Duration::from_secs(10))
        .send()
        .await?;

    let result: Value = response.json().await?;
    let task_name = result["playlist"]["name"]
        .as_str()
        .unwrap_or("Unknown_Playlist");

    let empty_vec = vec![];
    let track_ids = result["playlist"]["trackIds"]
        .as_array()
        .unwrap_or(&empty_vec);

    if track_ids.is_empty() {
        print_warn!("警告: 未在歌单中找到任何歌曲");
        return Ok(vec![]);
    }

    let song_ids: Vec<String> = track_ids
        .iter()
        .filter_map(|item| item["id"].as_u64().map(|id| id.to_string()))
        .collect();

    print_success!(
        "成功: 在歌单《{}》中找到 {} 首歌曲",
        task_name,
        song_ids.len()
    );

    Ok(song_ids
        .into_iter()
        .map(|song_id| format!("https://music.163.com/song?id={}", song_id))
        .collect())
}

async fn fetch_album_data(
    album_id: &str,
    client: &reqwest::Client,
) -> Result<Vec<String>, Box<dyn std::error::Error>> {
    print_info!("✓ 识别为专辑，正在获取歌曲列表...");

    let album_url = format!("https://music.163.com/album?id={}", album_id);

    let response = client
        .get(&album_url)
        .header("User-Agent", USER_AGENT)
        .header("Referer", "https://music.163.com/")
        .header("Accept-Language", "zh-CN,zh;q=0.9")
        .send()
        .await?;

    let html = response.text().await?;
    let document = Html::parse_document(&html);

    // 获取专辑名称
    let album_name_selector = Selector::parse("h2.f-ff2").unwrap();
    let album_name = document
        .select(&album_name_selector)
        .next()
        .map(|el| el.text().collect::<String>().trim().to_string())
        .unwrap_or_else(|| "Unknown_Album".to_string());

    // 获取歌曲列表
    let textarea_selector = Selector::parse("textarea#song-list-pre-data").unwrap();
    let textarea = document
        .select(&textarea_selector)
        .next()
        .ok_or("无法在专辑页面中找到歌曲列表")?;

    let tracks_json = textarea.text().collect::<String>();
    let tracks: Vec<Value> = serde_json::from_str(&tracks_json)?;

    let song_ids: Vec<String> = tracks
        .iter()
        .filter_map(|track| {
            if let Some(id_str) = track["id"].as_str() {
                Some(id_str.to_string())
            } else if let Some(id_num) = track["id"].as_u64() {
                Some(id_num.to_string())
            } else {
                None
            }
        })
        .collect();

    print_success!(
        "成功: 在专辑《{}》中找到 {} 首歌曲",
        album_name,
        song_ids.len()
    );

    Ok(song_ids
        .into_iter()
        .map(|song_id| format!("https://music.163.com/song?id={}", song_id))
        .collect())
}