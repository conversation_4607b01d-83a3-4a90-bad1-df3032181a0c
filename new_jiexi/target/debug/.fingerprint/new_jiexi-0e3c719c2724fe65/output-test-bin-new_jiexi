{"$message_type":"diagnostic","message":"this import is redundant","code":{"code":"clippy::single_component_path_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/api.rs","byte_start":18,"byte_end":30,"line_start":2,"line_end":2,"column_start":1,"column_end":13,"is_primary":true,"text":[{"text":"use reqwest;","highlight_start":1,"highlight_end":13}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_component_path_imports","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::single_component_path_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove it entirely","code":null,"level":"help","spans":[{"file_name":"src/api.rs","byte_start":18,"byte_end":31,"line_start":2,"line_end":3,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"use reqwest;","highlight_start":1,"highlight_end":13},{"text":"use scraper::{Html, Selector};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this import is redundant\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/api.rs:2:1\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse reqwest;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: remove it entirely\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#single_component_path_imports\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::single_component_path_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"constant `CONCURRENT_LIMIT` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/config.rs","byte_start":32,"byte_end":48,"line_start":2,"line_end":2,"column_start":11,"column_end":27,"is_primary":true,"text":[{"text":"pub const CONCURRENT_LIMIT: usize = 2;","highlight_start":11,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: constant `CONCURRENT_LIMIT` is never used\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/config.rs:2:11\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m2\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub const CONCURRENT_LIMIT: usize = 2;\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m           \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/crypto.rs","byte_start":1539,"byte_end":1569,"line_start":42,"line_end":42,"column_start":22,"column_end":52,"is_primary":true,"text":[{"text":"    let hex_result = format!(\"{:x}\", encrypted_int);","highlight_start":22,"highlight_end":52}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::uninlined_format_args)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/crypto.rs","byte_start":1549,"byte_end":1549,"line_start":42,"line_end":42,"column_start":32,"column_end":32,"is_primary":true,"text":[{"text":"    let hex_result = format!(\"{:x}\", encrypted_int);","highlight_start":32,"highlight_end":32}],"label":null,"suggested_replacement":"encrypted_int","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/crypto.rs","byte_start":1553,"byte_end":1568,"line_start":42,"line_end":42,"column_start":36,"column_end":51,"is_primary":true,"text":[{"text":"    let hex_result = format!(\"{:x}\", encrypted_int);","highlight_start":36,"highlight_end":51}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/crypto.rs:42:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let hex_result = format!(\"{:x}\", encrypted_int);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::uninlined_format_args)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let hex_result = format!(\"{:x}\"\u001b[0m\u001b[0m\u001b[38;5;9m, encrypted_int\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m42\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let hex_result = format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mencrypted_int\u001b[0m\u001b[0m:x}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/crypto.rs","byte_start":1578,"byte_end":1609,"line_start":43,"line_end":43,"column_start":8,"column_end":39,"is_primary":true,"text":[{"text":"    Ok(format!(\"{:0>256}\", hex_result))","highlight_start":8,"highlight_end":39}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/crypto.rs","byte_start":1588,"byte_end":1588,"line_start":43,"line_end":43,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    Ok(format!(\"{:0>256}\", hex_result))","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"hex_result","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/crypto.rs","byte_start":1596,"byte_end":1608,"line_start":43,"line_end":43,"column_start":26,"column_end":38,"is_primary":true,"text":[{"text":"    Ok(format!(\"{:0>256}\", hex_result))","highlight_start":26,"highlight_end":38}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/crypto.rs:43:8\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Ok(format!(\"{:0>256}\", hex_result))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    Ok(format!(\"{:0>256}\"\u001b[0m\u001b[0m\u001b[38;5;9m, hex_result\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    Ok(format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10mhex_result\u001b[0m\u001b[0m:0>256}\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/api.rs","byte_start":3313,"byte_end":3365,"line_start":100,"line_end":100,"column_start":24,"column_end":76,"is_primary":true,"text":[{"text":"        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))","highlight_start":24,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/api.rs","byte_start":3353,"byte_end":3353,"line_start":100,"line_end":100,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"song_id","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/api.rs","byte_start":3355,"byte_end":3364,"line_start":100,"line_end":100,"column_start":66,"column_end":75,"is_primary":true,"text":[{"text":"        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))","highlight_start":66,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/api.rs:100:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map(|song_id| format!(\"https://music.163.com/song?id={}\"\u001b[0m\u001b[0m\u001b[38;5;9m, song_id\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map(|song_id| format!(\"https://music.163.com/song?id={\u001b[0m\u001b[0m\u001b[38;5;10msong_id\u001b[0m\u001b[0m}\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/api.rs","byte_start":3613,"byte_end":3667,"line_start":110,"line_end":110,"column_start":21,"column_end":75,"is_primary":true,"text":[{"text":"    let album_url = format!(\"https://music.163.com/album?id={}\", album_id);","highlight_start":21,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/api.rs","byte_start":3654,"byte_end":3654,"line_start":110,"line_end":110,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"    let album_url = format!(\"https://music.163.com/album?id={}\", album_id);","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"album_id","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/api.rs","byte_start":3656,"byte_end":3666,"line_start":110,"line_end":110,"column_start":64,"column_end":74,"is_primary":true,"text":[{"text":"    let album_url = format!(\"https://music.163.com/album?id={}\", album_id);","highlight_start":64,"highlight_end":74}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/api.rs:110:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let album_url = format!(\"https://music.163.com/album?id={}\", album_id);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let album_url = format!(\"https://music.163.com/album?id={}\"\u001b[0m\u001b[0m\u001b[38;5;9m, album_id\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m110\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let album_url = format!(\"https://music.163.com/album?id={\u001b[0m\u001b[0m\u001b[38;5;10malbum_id\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"manual implementation of `Option::map`","code":{"code":"clippy::manual_map","explanation":null},"level":"warning","spans":[{"file_name":"src/api.rs","byte_start":4878,"byte_end":5019,"line_start":146,"line_end":150,"column_start":20,"column_end":14,"is_primary":true,"text":[{"text":"            } else if let Some(id_num) = track[\"id\"].as_u64() {","highlight_start":20,"highlight_end":64},{"text":"                Some(id_num.to_string())","highlight_start":1,"highlight_end":41},{"text":"            } else {","highlight_start":1,"highlight_end":21},{"text":"                None","highlight_start":1,"highlight_end":21},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_map","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::manual_map)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"try","code":null,"level":"help","spans":[{"file_name":"src/api.rs","byte_start":4878,"byte_end":5019,"line_start":146,"line_end":150,"column_start":20,"column_end":14,"is_primary":true,"text":[{"text":"            } else if let Some(id_num) = track[\"id\"].as_u64() {","highlight_start":20,"highlight_end":64},{"text":"                Some(id_num.to_string())","highlight_start":1,"highlight_end":41},{"text":"            } else {","highlight_start":1,"highlight_end":21},{"text":"                None","highlight_start":1,"highlight_end":21},{"text":"            }","highlight_start":1,"highlight_end":14}],"label":null,"suggested_replacement":"{ track[\"id\"].as_u64().map(|id_num| id_num.to_string()) }","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: manual implementation of `Option::map`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/api.rs:146:20\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m            } else if let Some(id_num) = track[\"id\"].as_u64() {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m ____________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m147\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                Some(id_num.to_string())\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m148\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            } else {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m149\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                None\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m150\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_____________^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: try: `{ track[\"id\"].as_u64().map(|id_num| id_num.to_string()) }`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#manual_map\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::manual_map)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/api.rs","byte_start":5242,"byte_end":5294,"line_start":162,"line_end":162,"column_start":24,"column_end":76,"is_primary":true,"text":[{"text":"        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))","highlight_start":24,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/api.rs","byte_start":5282,"byte_end":5282,"line_start":162,"line_end":162,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"song_id","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/api.rs","byte_start":5284,"byte_end":5293,"line_start":162,"line_end":162,"column_start":66,"column_end":75,"is_primary":true,"text":[{"text":"        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))","highlight_start":66,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/api.rs:162:24\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map(|song_id| format!(\"https://music.163.com/song?id={}\", song_id))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map(|song_id| format!(\"https://music.163.com/song?id={}\"\u001b[0m\u001b[0m\u001b[38;5;9m, song_id\u001b[0m\u001b[0m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m162\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map(|song_id| format!(\"https://music.163.com/song?id={\u001b[0m\u001b[0m\u001b[38;5;10msong_id\u001b[0m\u001b[0m}\"))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":4174,"byte_end":4216,"line_start":119,"line_end":119,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"创建临时目录失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":4210,"byte_end":4210,"line_start":119,"line_end":119,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"创建临时目录失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":4212,"byte_end":4215,"line_start":119,"line_end":119,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"创建临时目录失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:119:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"创建临时目录失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"创建临时目录失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m119\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"创建临时目录失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":4355,"byte_end":4394,"line_start":123,"line_end":123,"column_start":60,"column_end":85,"is_primary":true,"text":[{"text":"        launch_browser().map_err(|e| ProcessingError::new(&format!(\"启动浏览器失败: {}\", e)))?;","highlight_start":60,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":4388,"byte_end":4388,"line_start":123,"line_end":123,"column_start":79,"column_end":79,"is_primary":true,"text":[{"text":"        launch_browser().map_err(|e| ProcessingError::new(&format!(\"启动浏览器失败: {}\", e)))?;","highlight_start":79,"highlight_end":79}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":4390,"byte_end":4393,"line_start":123,"line_end":123,"column_start":81,"column_end":84,"is_primary":true,"text":[{"text":"        launch_browser().map_err(|e| ProcessingError::new(&format!(\"启动浏览器失败: {}\", e)))?;","highlight_start":81,"highlight_end":84}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:123:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        launch_browser().map_err(|e| ProcessingError::new(&format!(\"启动浏览器失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        launch_browser().map_err(|e| ProcessingError::new(&format!(\"启动浏览器失败: {}\", e)))?\u001b[0m\u001b[0m\u001b[38;5;9m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m123\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        launch_browser().map_err(|e| ProcessingError::new(&format!(\"启动浏览器失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":4484,"byte_end":4523,"line_start":127,"line_end":127,"column_start":44,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"创建标签页失败: {}\", e)))?;","highlight_start":44,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":4517,"byte_end":4517,"line_start":127,"line_end":127,"column_start":63,"column_end":63,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"创建标签页失败: {}\", e)))?;","highlight_start":63,"highlight_end":63}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":4519,"byte_end":4522,"line_start":127,"line_end":127,"column_start":65,"column_end":68,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"创建标签页失败: {}\", e)))?;","highlight_start":65,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:127:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"创建标签页失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"创建标签页失败: {}\", e)))?\u001b[0m\u001b[0m\u001b[38;5;9m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m127\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"创建标签页失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":4632,"byte_end":4674,"line_start":131,"line_end":131,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"设置下载行为失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":4668,"byte_end":4668,"line_start":131,"line_end":131,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"设置下载行为失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":4670,"byte_end":4673,"line_start":131,"line_end":131,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"设置下载行为失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:131:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"设置下载行为失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"设置下载行为失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m131\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"设置下载行为失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":4799,"byte_end":4839,"line_start":135,"line_end":135,"column_start":44,"column_end":76,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"设置User-Agent失败: {}\", e)))?;","highlight_start":44,"highlight_end":76}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":4833,"byte_end":4833,"line_start":135,"line_end":135,"column_start":70,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"设置User-Agent失败: {}\", e)))?;","highlight_start":70,"highlight_end":70}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":4835,"byte_end":4838,"line_start":135,"line_end":135,"column_start":72,"column_end":75,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"设置User-Agent失败: {}\", e)))?;","highlight_start":72,"highlight_end":75}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:135:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"设置User-Agent失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"设置User-Agent失败: {}\", e)\u001b[0m\u001b[0m\u001b[38;5;9m))?\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m135\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"设置User-Agent失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":4925,"byte_end":4970,"line_start":138,"line_end":138,"column_start":44,"column_end":71,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"导航到目标页面失败: {}\", e)))?;","highlight_start":44,"highlight_end":71}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":4964,"byte_end":4964,"line_start":138,"line_end":138,"column_start":65,"column_end":65,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"导航到目标页面失败: {}\", e)))?;","highlight_start":65,"highlight_end":65}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":4966,"byte_end":4969,"line_start":138,"line_end":138,"column_start":67,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"导航到目标页面失败: {}\", e)))?;","highlight_start":67,"highlight_end":70}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:138:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"导航到目标页面失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"导航到目标页面失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m138\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"导航到目标页面失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":5199,"byte_end":5235,"line_start":146,"line_end":146,"column_start":44,"column_end":68,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"页面加载失败: {}\", e)))?;","highlight_start":44,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":5229,"byte_end":5229,"line_start":146,"line_end":146,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"页面加载失败: {}\", e)))?;","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":5231,"byte_end":5234,"line_start":146,"line_end":146,"column_start":64,"column_end":67,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"页面加载失败: {}\", e)))?;","highlight_start":64,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:146:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"页面加载失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"页面加载失败: {}\", e)))\u001b[0m\u001b[0m\u001b[38;5;9m?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m146\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"页面加载失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":5355,"byte_end":5388,"line_start":151,"line_end":151,"column_start":44,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"填写URL失败: {}\", e)))?;","highlight_start":44,"highlight_end":69}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":5382,"byte_end":5382,"line_start":151,"line_end":151,"column_start":63,"column_end":63,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"填写URL失败: {}\", e)))?;","highlight_start":63,"highlight_end":63}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":5384,"byte_end":5387,"line_start":151,"line_end":151,"column_start":65,"column_end":68,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"填写URL失败: {}\", e)))?;","highlight_start":65,"highlight_end":68}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:151:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"填写URL失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"填写URL失败: {}\", e)\u001b[0m\u001b[0m\u001b[38;5;9m))?\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m151\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"填写URL失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":5511,"byte_end":5547,"line_start":156,"line_end":156,"column_start":44,"column_end":68,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"选择音质失败: {}\", e)))?;","highlight_start":44,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":5541,"byte_end":5541,"line_start":156,"line_end":156,"column_start":62,"column_end":62,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"选择音质失败: {}\", e)))?;","highlight_start":62,"highlight_end":62}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":5543,"byte_end":5546,"line_start":156,"line_end":156,"column_start":64,"column_end":67,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"选择音质失败: {}\", e)))?;","highlight_start":64,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:156:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"选择音质失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"选择音质失败: {}\", e)))\u001b[0m\u001b[0m\u001b[38;5;9m?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m156\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"选择音质失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":5668,"byte_end":5710,"line_start":161,"line_end":161,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"选择下载类型失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":5704,"byte_end":5704,"line_start":161,"line_end":161,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"选择下载类型失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":5706,"byte_end":5709,"line_start":161,"line_end":161,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"选择下载类型失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:161:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"选择下载类型失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"选择下载类型失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m161\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"选择下载类型失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":5823,"byte_end":5865,"line_start":166,"line_end":166,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"点击解析按钮失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":5859,"byte_end":5859,"line_start":166,"line_end":166,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"点击解析按钮失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":5861,"byte_end":5864,"line_start":166,"line_end":166,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"点击解析按钮失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:166:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"点击解析按钮失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"点击解析按钮失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m166\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"点击解析按钮失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":5980,"byte_end":6022,"line_start":171,"line_end":171,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"点击确认按钮失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":6016,"byte_end":6016,"line_start":171,"line_end":171,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"点击确认按钮失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":6018,"byte_end":6021,"line_start":171,"line_end":171,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"点击确认按钮失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:171:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"点击确认按钮失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"点击确认按钮失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m171\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"点击确认按钮失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":6147,"byte_end":6189,"line_start":176,"line_end":176,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"等待下载按钮失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":6183,"byte_end":6183,"line_start":176,"line_end":176,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"等待下载按钮失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":6185,"byte_end":6188,"line_start":176,"line_end":176,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"等待下载按钮失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:176:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"等待下载按钮失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"等待下载按钮失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"等待下载按钮失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":6325,"byte_end":6355,"line_start":181,"line_end":181,"column_start":44,"column_end":66,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"下载失败: {}\", e)))?;","highlight_start":44,"highlight_end":66}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":6349,"byte_end":6349,"line_start":181,"line_end":181,"column_start":60,"column_end":60,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"下载失败: {}\", e)))?;","highlight_start":60,"highlight_end":60}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":6351,"byte_end":6354,"line_start":181,"line_end":181,"column_start":62,"column_end":65,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"下载失败: {}\", e)))?;","highlight_start":62,"highlight_end":65}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:181:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m181\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"下载失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m181\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"下载失败: {}\", e)\u001b[0m\u001b[0m\u001b[38;5;9m))?\u001b[0m\u001b[0m;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m181\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"下载失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":6483,"byte_end":6525,"line_start":186,"line_end":186,"column_start":44,"column_end":70,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"处理下载文件失败: {}\", e)))?;","highlight_start":44,"highlight_end":70}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":6519,"byte_end":6519,"line_start":186,"line_end":186,"column_start":64,"column_end":64,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"处理下载文件失败: {}\", e)))?;","highlight_start":64,"highlight_end":64}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":6521,"byte_end":6524,"line_start":186,"line_end":186,"column_start":66,"column_end":69,"is_primary":true,"text":[{"text":"        .map_err(|e| ProcessingError::new(&format!(\"处理下载文件失败: {}\", e)))?;","highlight_start":66,"highlight_end":69}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:186:44\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"处理下载文件失败: {}\", e)))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"处理下载文件失败: {}\", e)))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m186\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| ProcessingError::new(&format!(\"处理下载文件失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\")))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":8053,"byte_end":8102,"line_start":229,"line_end":229,"column_start":28,"column_end":77,"is_primary":true,"text":[{"text":"    let download_dir_arg = format!(\"--download-directory={}\", download_path);","highlight_start":28,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":8084,"byte_end":8084,"line_start":229,"line_end":229,"column_start":59,"column_end":59,"is_primary":true,"text":[{"text":"    let download_dir_arg = format!(\"--download-directory={}\", download_path);","highlight_start":59,"highlight_end":59}],"label":null,"suggested_replacement":"download_path","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":8086,"byte_end":8101,"line_start":229,"line_end":229,"column_start":61,"column_end":76,"is_primary":true,"text":[{"text":"    let download_dir_arg = format!(\"--download-directory={}\", download_path);","highlight_start":61,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:229:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let download_dir_arg = format!(\"--download-directory={}\", download_path);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let download_dir_arg = format!(\"--download-directory={}\"\u001b[0m\u001b[0m\u001b[38;5;9m, download_path\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m229\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let download_dir_arg = format!(\"--download-directory={\u001b[0m\u001b[0m\u001b[38;5;10mdownload_path\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":9701,"byte_end":9752,"line_start":268,"line_end":268,"column_start":22,"column_end":51,"is_primary":true,"text":[{"text":"        .map_err(|e| format!(\"构建浏览器启动选项失败: {}\", e))?;","highlight_start":22,"highlight_end":51}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":9746,"byte_end":9746,"line_start":268,"line_end":268,"column_start":45,"column_end":45,"is_primary":true,"text":[{"text":"        .map_err(|e| format!(\"构建浏览器启动选项失败: {}\", e))?;","highlight_start":45,"highlight_end":45}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":9748,"byte_end":9751,"line_start":268,"line_end":268,"column_start":47,"column_end":50,"is_primary":true,"text":[{"text":"        .map_err(|e| format!(\"构建浏览器启动选项失败: {}\", e))?;","highlight_start":47,"highlight_end":50}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:268:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .map_err(|e| format!(\"构建浏览器启动选项失败: {}\", e))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .map_err(|e| format!(\"构建浏览器启动选项失败: {}\", e))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m268\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .map_err(|e| format!(\"构建浏览器启动选项失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\"))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":9816,"byte_end":9855,"line_start":270,"line_end":270,"column_start":60,"column_end":85,"is_primary":true,"text":[{"text":"    let browser = Browser::new(launch_options).map_err(|e| format!(\"启动浏览器失败: {}\", e))?;","highlight_start":60,"highlight_end":85}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":9849,"byte_end":9849,"line_start":270,"line_end":270,"column_start":79,"column_end":79,"is_primary":true,"text":[{"text":"    let browser = Browser::new(launch_options).map_err(|e| format!(\"启动浏览器失败: {}\", e))?;","highlight_start":79,"highlight_end":79}],"label":null,"suggested_replacement":"e","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":9851,"byte_end":9854,"line_start":270,"line_end":270,"column_start":81,"column_end":84,"is_primary":true,"text":[{"text":"    let browser = Browser::new(launch_options).map_err(|e| format!(\"启动浏览器失败: {}\", e))?;","highlight_start":81,"highlight_end":84}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:270:60\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let browser = Browser::new(launch_options).map_err(|e| format!(\"启动浏览器失败: {}\", e))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let browser = Browser::new(launch_options).map_err(|e| format!(\"启动浏览器失败: {}\", e))?;\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m270\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let browser = Browser::new(launch_options).map_err(|e| format!(\"启动浏览器失败: {\u001b[0m\u001b[0m\u001b[38;5;10me\u001b[0m\u001b[0m}\"))?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":10458,"byte_end":10742,"line_start":289,"line_end":295,"column_start":18,"column_end":6,"is_primary":true,"text":[{"text":"    let script = format!(","highlight_start":18,"highlight_end":26},{"text":"        r#\"","highlight_start":1,"highlight_end":12},{"text":"        document.querySelector(\"[placeholder*='请输入网易云音乐链接']\").value = '{}';","highlight_start":1,"highlight_end":76},{"text":"        document.querySelector(\"[placeholder*='请输入网易云音乐链接']\").dispatchEvent(new Event('input', {{ bubbles: true }}));","highlight_start":1,"highlight_end":118},{"text":"        \"#,","highlight_start":1,"highlight_end":12},{"text":"        url","highlight_start":1,"highlight_end":12},{"text":"    );","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":10571,"byte_end":10571,"line_start":291,"line_end":291,"column_start":73,"column_end":73,"is_primary":true,"text":[{"text":"        document.querySelector(\"[placeholder*='请输入网易云音乐链接']\").value = '{}';","highlight_start":73,"highlight_end":73}],"label":null,"suggested_replacement":"url","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":10723,"byte_end":10736,"line_start":293,"line_end":294,"column_start":11,"column_end":12,"is_primary":true,"text":[{"text":"        \"#,","highlight_start":11,"highlight_end":12},{"text":"        url","highlight_start":1,"highlight_end":12}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:289:18\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m289\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let script = format!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m __________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m290\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m291\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        document.querySelector(\"[placeholder*='请输入网易云音乐链接']\").value = '{}';\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m292\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        document.querySelector(\"[placeholder*='请输入网易云音乐链接']\").dispatchEvent(new Event('input', {{ bubbles: true }}));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m293\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        \"#,\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m294\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        url\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m295\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    );\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_____^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":11124,"byte_end":11622,"line_start":308,"line_end":322,"column_start":33,"column_end":6,"is_primary":true,"text":[{"text":"    let select_quality_script = format!(","highlight_start":33,"highlight_end":41},{"text":"        r#\"","highlight_start":1,"highlight_end":12},{"text":"        const options = Array.from(document.querySelectorAll('li'));","highlight_start":1,"highlight_end":69},{"text":"        const targetOption = options.find(li => li.textContent.includes('{}'));","highlight_start":1,"highlight_end":80},{"text":"        if (targetOption) {{","highlight_start":1,"highlight_end":29},{"text":"            targetOption.click();","highlight_start":1,"highlight_end":34},{"text":"        }} else {{","highlight_start":1,"highlight_end":19},{"text":"            const fallbackOption = options.find(li => li.textContent.includes('高解析度无损(VIP)'));","highlight_start":1,"highlight_end":95},{"text":"            if (fallbackOption) {{","highlight_start":1,"highlight_end":35},{"text":"                fallbackOption.click();","highlight_start":1,"highlight_end":40},{"text":"            }}","highlight_start":1,"highlight_end":15},{"text":"        }}","highlight_start":1,"highlight_end":11},{"text":"        \"#,","highlight_start":1,"highlight_end":12},{"text":"        quality_text","highlight_start":1,"highlight_end":21},{"text":"    );","highlight_start":1,"highlight_end":6}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":11288,"byte_end":11288,"line_start":311,"line_end":311,"column_start":75,"column_end":75,"is_primary":true,"text":[{"text":"        const targetOption = options.find(li => li.textContent.includes('{}'));","highlight_start":75,"highlight_end":75}],"label":null,"suggested_replacement":"quality_text","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":11594,"byte_end":11616,"line_start":320,"line_end":321,"column_start":11,"column_end":21,"is_primary":true,"text":[{"text":"        \"#,","highlight_start":11,"highlight_end":12},{"text":"        quality_text","highlight_start":1,"highlight_end":21}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:308:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m308\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m    let select_quality_script = format!(\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m _________________________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m309\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        r#\"\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m310\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        const options = Array.from(document.querySelectorAll('li'));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m311\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        const targetOption = options.find(li => li.textContent.includes('{}'));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m321\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        quality_text\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m322\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    );\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_____^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `if` statement can be collapsed","code":{"code":"clippy::collapsible_if","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":14572,"byte_end":15089,"line_start":396,"line_end":407,"column_start":21,"column_end":22,"is_primary":true,"text":[{"text":"                    if (file_name.ends_with(\".zip\") || file_name.ends_with(\".rar\"))","highlight_start":21,"highlight_end":84},{"text":"                        && file_size > 1000","highlight_start":1,"highlight_end":44},{"text":"                    {","highlight_start":1,"highlight_end":22},{"text":"                        if is_download_complete(&path)? {","highlight_start":1,"highlight_end":58},{"text":"                            print_success!(","highlight_start":1,"highlight_end":44},{"text":"                                \"✅ 文件下载完成: {} ({} KB)\",","highlight_start":1,"highlight_end":56},{"text":"                                file_name,","highlight_start":1,"highlight_end":43},{"text":"                                file_size / 1024","highlight_start":1,"highlight_end":49},{"text":"                            );","highlight_start":1,"highlight_end":31},{"text":"                            return Ok(path);","highlight_start":1,"highlight_end":45},{"text":"                        }","highlight_start":1,"highlight_end":26},{"text":"                    }","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::collapsible_if)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"collapse nested if block","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":14679,"byte_end":14701,"line_start":397,"line_end":398,"column_start":44,"column_end":22,"is_primary":true,"text":[{"text":"                        && file_size > 1000","highlight_start":44,"highlight_end":44},{"text":"                    {","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":15067,"byte_end":15089,"line_start":406,"line_end":407,"column_start":26,"column_end":22,"is_primary":true,"text":[{"text":"                        }","highlight_start":26,"highlight_end":26},{"text":"                    }","highlight_start":1,"highlight_end":22}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":14726,"byte_end":14728,"line_start":399,"line_end":399,"column_start":25,"column_end":27,"is_primary":true,"text":[{"text":"                        if is_download_complete(&path)? {","highlight_start":25,"highlight_end":27}],"label":null,"suggested_replacement":"&&","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this `if` statement can be collapsed\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:396:21\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m396\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    if (file_name.ends_with(\".zip\") || file_name.ends_with(\".rar\"))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m397\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        && file_size > 1000\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m398\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m399\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                        if is_download_complete(&path)? {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m407\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_____________________^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#collapsible_if\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::collapsible_if)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: collapse nested if block\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m397\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                        && file_size > 1000\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m398\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                        \u001b[0m\u001b[0m\u001b[38;5;10m&&\u001b[0m\u001b[0m is_download_complete(&path)? {\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m399\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             print_success!(\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m404\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                             return Ok(path);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m405\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m                        }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/downloader.rs","byte_start":15334,"byte_end":15390,"line_start":418,"line_end":418,"column_start":9,"column_end":41,"is_primary":true,"text":[{"text":"    Err(format!(\"下载超时，未找到下载文件: {}\", url).into())","highlight_start":9,"highlight_end":41}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/downloader.rs","byte_start":15382,"byte_end":15382,"line_start":418,"line_end":418,"column_start":33,"column_end":33,"is_primary":true,"text":[{"text":"    Err(format!(\"下载超时，未找到下载文件: {}\", url).into())","highlight_start":33,"highlight_end":33}],"label":null,"suggested_replacement":"url","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/downloader.rs","byte_start":15384,"byte_end":15389,"line_start":418,"line_end":418,"column_start":35,"column_end":40,"is_primary":true,"text":[{"text":"    Err(format!(\"下载超时，未找到下载文件: {}\", url).into())","highlight_start":35,"highlight_end":40}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/downloader.rs:418:9\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m418\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    Err(format!(\"下载超时，未找到下载文件: {}\", url).into())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m418\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    Err(format!(\"下载超时，未找到下载文件: {}\", url).into(\u001b[0m\u001b[0m\u001b[38;5;9m))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m418\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    Err(format!(\"下载超时，未找到下载文件: {\u001b[0m\u001b[0m\u001b[38;5;10murl\u001b[0m\u001b[0m}\").into())\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `map_or` can be simplified","code":{"code":"clippy::unnecessary_map_or","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":1451,"byte_end":1510,"line_start":43,"line_end":43,"column_start":25,"column_end":84,"is_primary":true,"text":[{"text":"        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"flac\"))","highlight_start":25,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::unnecessary_map_or)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"use is_some_and instead","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":1476,"byte_end":1482,"line_start":43,"line_end":43,"column_start":50,"column_end":56,"is_primary":true,"text":[{"text":"        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"flac\"))","highlight_start":50,"highlight_end":56}],"label":null,"suggested_replacement":"is_some_and","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":1483,"byte_end":1490,"line_start":43,"line_end":43,"column_start":57,"column_end":64,"is_primary":true,"text":[{"text":"        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"flac\"))","highlight_start":57,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this `map_or` can be simplified\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:43:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"flac\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::unnecessary_map_or)]` on by default\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use is_some_and instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .filter(|entry| entry.path().extension().\u001b[0m\u001b[0m\u001b[38;5;9mmap_or\u001b[0m\u001b[0m(\u001b[0m\u001b[0m\u001b[38;5;9mfalse, \u001b[0m\u001b[0m|ext| ext == \"flac\"))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m43\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .filter(|entry| entry.path().extension().\u001b[0m\u001b[0m\u001b[38;5;10mis_some_and\u001b[0m\u001b[0m(|ext| ext == \"flac\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":1895,"byte_end":1925,"line_start":54,"line_end":54,"column_start":38,"column_end":68,"is_primary":true,"text":[{"text":"    let output_path = music_dir.join(format!(\"{}.flac\", song_title));","highlight_start":38,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":1905,"byte_end":1905,"line_start":54,"line_end":54,"column_start":48,"column_end":48,"is_primary":true,"text":[{"text":"    let output_path = music_dir.join(format!(\"{}.flac\", song_title));","highlight_start":48,"highlight_end":48}],"label":null,"suggested_replacement":"song_title","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":1912,"byte_end":1924,"line_start":54,"line_end":54,"column_start":55,"column_end":67,"is_primary":true,"text":[{"text":"    let output_path = music_dir.join(format!(\"{}.flac\", song_title));","highlight_start":55,"highlight_end":67}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:54:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let output_path = music_dir.join(format!(\"{}.flac\", song_title));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let output_path = music_dir.join(format!(\"{}.flac\"\u001b[0m\u001b[0m\u001b[38;5;9m, song_title\u001b[0m\u001b[0m));\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m54\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let output_path = music_dir.join(format!(\"{\u001b[0m\u001b[0m\u001b[38;5;10msong_title\u001b[0m\u001b[0m}.flac\"));\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":1984,"byte_end":1997,"line_start":57,"line_end":57,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    cmd.args(&[\"-y\", \"-i\"]).arg(&flac_path);","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"`#[warn(clippy::needless_borrows_for_generic_args)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":1984,"byte_end":1997,"line_start":57,"line_end":57,"column_start":14,"column_end":27,"is_primary":true,"text":[{"text":"    cmd.args(&[\"-y\", \"-i\"]).arg(&flac_path);","highlight_start":14,"highlight_end":27}],"label":null,"suggested_replacement":"[\"-y\", \"-i\"]","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:57:14\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m57\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    cmd.args(&[\"-y\", \"-i\"]).arg(&flac_path);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `[\"-y\", \"-i\"]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(clippy::needless_borrows_for_generic_args)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `map_or` can be simplified","code":{"code":"clippy::unnecessary_map_or","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":2179,"byte_end":2321,"line_start":63,"line_end":66,"column_start":13,"column_end":84,"is_primary":true,"text":[{"text":"            entry","highlight_start":13,"highlight_end":18},{"text":"                .path()","highlight_start":1,"highlight_end":24},{"text":"                .extension()","highlight_start":1,"highlight_end":29},{"text":"                .map_or(false, |ext| ext == \"jpg\" || ext == \"jpeg\" || ext == \"png\")","highlight_start":1,"highlight_end":84}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"use is_some_and instead","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":2255,"byte_end":2261,"line_start":66,"line_end":66,"column_start":18,"column_end":24,"is_primary":true,"text":[{"text":"                .map_or(false, |ext| ext == \"jpg\" || ext == \"jpeg\" || ext == \"png\")","highlight_start":18,"highlight_end":24}],"label":null,"suggested_replacement":"is_some_and","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":2262,"byte_end":2269,"line_start":66,"line_end":66,"column_start":25,"column_end":32,"is_primary":true,"text":[{"text":"                .map_or(false, |ext| ext == \"jpg\" || ext == \"jpeg\" || ext == \"png\")","highlight_start":25,"highlight_end":32}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this `map_or` can be simplified\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:63:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m63\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m/\u001b[0m\u001b[0m \u001b[0m\u001b[0m            entry\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m64\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .path()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m65\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .extension()\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                .map_or(false, |ext| ext == \"jpg\" || ext == \"jpeg\" || ext == \"png\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|___________________________________________________________________________________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use is_some_and instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m                .\u001b[0m\u001b[0m\u001b[38;5;9mmap_or\u001b[0m\u001b[0m(\u001b[0m\u001b[0m\u001b[38;5;9mfalse, \u001b[0m\u001b[0m|ext| ext == \"jpg\" || ext == \"jpeg\" || ext == \"png\")\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m66\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m                .\u001b[0m\u001b[0m\u001b[38;5;10mis_some_and\u001b[0m\u001b[0m(|ext| ext == \"jpg\" || ext == \"jpeg\" || ext == \"png\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":2402,"byte_end":2409,"line_start":71,"line_end":71,"column_start":18,"column_end":25,"is_primary":true,"text":[{"text":"        cmd.args(&[\"-i\"]).arg(jpg_files[0].path());","highlight_start":18,"highlight_end":25}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":2402,"byte_end":2409,"line_start":71,"line_end":71,"column_start":18,"column_end":25,"is_primary":true,"text":[{"text":"        cmd.args(&[\"-i\"]).arg(jpg_files[0].path());","highlight_start":18,"highlight_end":25}],"label":null,"suggested_replacement":"[\"-i\"]","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:71:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m71\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        cmd.args(&[\"-i\"]).arg(jpg_files[0].path());\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `[\"-i\"]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":2454,"byte_end":2508,"line_start":72,"line_end":72,"column_start":18,"column_end":72,"is_primary":true,"text":[{"text":"        cmd.args(&[\"-map_metadata\", \"-1\", \"-map\", \"0:a\", \"-map\", \"1:v\"]);","highlight_start":18,"highlight_end":72}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":2454,"byte_end":2508,"line_start":72,"line_end":72,"column_start":18,"column_end":72,"is_primary":true,"text":[{"text":"        cmd.args(&[\"-map_metadata\", \"-1\", \"-map\", \"0:a\", \"-map\", \"1:v\"]);","highlight_start":18,"highlight_end":72}],"label":null,"suggested_replacement":"[\"-map_metadata\", \"-1\", \"-map\", \"0:a\", \"-map\", \"1:v\"]","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:72:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m72\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        cmd.args(&[\"-map_metadata\", \"-1\", \"-map\", \"0:a\", \"-map\", \"1:v\"]);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `[\"-map_metadata\", \"-1\", \"-map\", \"0:a\", \"-map\", \"1:v\"]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":2528,"byte_end":2678,"line_start":73,"line_end":80,"column_start":18,"column_end":10,"is_primary":true,"text":[{"text":"        cmd.args(&[","highlight_start":18,"highlight_end":20},{"text":"            \"-c:a\",","highlight_start":1,"highlight_end":20},{"text":"            \"copy\",","highlight_start":1,"highlight_end":20},{"text":"            \"-c:v\",","highlight_start":1,"highlight_end":20},{"text":"            \"copy\",","highlight_start":1,"highlight_end":20},{"text":"            \"-disposition:v\",","highlight_start":1,"highlight_end":30},{"text":"            \"attached_pic\",","highlight_start":1,"highlight_end":28},{"text":"        ]);","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":2528,"byte_end":2678,"line_start":73,"line_end":80,"column_start":18,"column_end":10,"is_primary":true,"text":[{"text":"        cmd.args(&[","highlight_start":18,"highlight_end":20},{"text":"            \"-c:a\",","highlight_start":1,"highlight_end":20},{"text":"            \"copy\",","highlight_start":1,"highlight_end":20},{"text":"            \"-c:v\",","highlight_start":1,"highlight_end":20},{"text":"            \"copy\",","highlight_start":1,"highlight_end":20},{"text":"            \"-disposition:v\",","highlight_start":1,"highlight_end":30},{"text":"            \"attached_pic\",","highlight_start":1,"highlight_end":28},{"text":"        ]);","highlight_start":1,"highlight_end":10}],"label":null,"suggested_replacement":"[\n            \"-c:a\",\n            \"copy\",\n            \"-c:v\",\n            \"copy\",\n            \"-disposition:v\",\n            \"attached_pic\",\n        ]","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:73:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m   \u001b[0m\u001b[0m        cmd.args(&[\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m __________________^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"-c:a\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"copy\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"-c:v\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            \"attached_pic\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        ]);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33m|_________^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m73\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~ \u001b[0m\u001b[0m        cmd.args(\u001b[0m\u001b[0m\u001b[38;5;10m[\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m74\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             \"-c:a\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m75\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             \"copy\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m76\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             \"-c:v\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m77\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             \"copy\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m78\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             \"-disposition:v\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m79\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+             \"attached_pic\",\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m80\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m~         ]\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":2711,"byte_end":2728,"line_start":82,"line_end":82,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"        cmd.args(&[\"-c:a\", \"copy\"]);","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":2711,"byte_end":2728,"line_start":82,"line_end":82,"column_start":18,"column_end":35,"is_primary":true,"text":[{"text":"        cmd.args(&[\"-c:a\", \"copy\"]);","highlight_start":18,"highlight_end":35}],"label":null,"suggested_replacement":"[\"-c:a\", \"copy\"]","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:82:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        cmd.args(&[\"-c:a\", \"copy\"]);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `[\"-c:a\", \"copy\"]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `map_or` can be simplified","code":{"code":"clippy::unnecessary_map_or","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":2886,"byte_end":2944,"line_start":88,"line_end":88,"column_start":25,"column_end":83,"is_primary":true,"text":[{"text":"        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"lrc\"))","highlight_start":25,"highlight_end":83}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"use is_some_and instead","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":2911,"byte_end":2917,"line_start":88,"line_end":88,"column_start":50,"column_end":56,"is_primary":true,"text":[{"text":"        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"lrc\"))","highlight_start":50,"highlight_end":56}],"label":null,"suggested_replacement":"is_some_and","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":2918,"byte_end":2925,"line_start":88,"line_end":88,"column_start":57,"column_end":64,"is_primary":true,"text":[{"text":"        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"lrc\"))","highlight_start":57,"highlight_end":64}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this `map_or` can be simplified\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:88:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        .filter(|entry| entry.path().extension().map_or(false, |ext| ext == \"lrc\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use is_some_and instead\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m        .filter(|entry| entry.path().extension().\u001b[0m\u001b[0m\u001b[38;5;9mmap_or\u001b[0m\u001b[0m(\u001b[0m\u001b[0m\u001b[38;5;9mfalse, \u001b[0m\u001b[0m|ext| ext == \"lrc\"))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m        .filter(|entry| entry.path().extension().\u001b[0m\u001b[0m\u001b[38;5;10mis_some_and\u001b[0m\u001b[0m(|ext| ext == \"lrc\"))\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":3045,"byte_end":3065,"line_start":92,"line_end":92,"column_start":48,"column_end":68,"is_primary":true,"text":[{"text":"        if let Ok(lyrics) = fs::read_to_string(&lrc_files[0].path()) {","highlight_start":48,"highlight_end":68}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":3045,"byte_end":3065,"line_start":92,"line_end":92,"column_start":48,"column_end":68,"is_primary":true,"text":[{"text":"        if let Ok(lyrics) = fs::read_to_string(&lrc_files[0].path()) {","highlight_start":48,"highlight_end":68}],"label":null,"suggested_replacement":"lrc_files[0].path()","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:92:48\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m92\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Ok(lyrics) = fs::read_to_string(&lrc_files[0].path()) {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                                \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `lrc_files[0].path()`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"the borrowed expression implements the required traits","code":{"code":"clippy::needless_borrows_for_generic_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":3171,"byte_end":3224,"line_start":94,"line_end":94,"column_start":22,"column_end":75,"is_primary":true,"text":[{"text":"            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);","highlight_start":22,"highlight_end":75}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":3171,"byte_end":3224,"line_start":94,"line_end":94,"column_start":22,"column_end":75,"is_primary":true,"text":[{"text":"            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);","highlight_start":22,"highlight_end":75}],"label":null,"suggested_replacement":"[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: the borrowed expression implements the required traits\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:94:22\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mhelp: change this to: `[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#needless_borrows_for_generic_args\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":3187,"byte_end":3223,"line_start":94,"line_end":94,"column_start":38,"column_end":74,"is_primary":true,"text":[{"text":"            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);","highlight_start":38,"highlight_end":74}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":3204,"byte_end":3204,"line_start":94,"line_end":94,"column_start":55,"column_end":55,"is_primary":true,"text":[{"text":"            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);","highlight_start":55,"highlight_end":55}],"label":null,"suggested_replacement":"escaped_lyrics","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":3206,"byte_end":3222,"line_start":94,"line_end":94,"column_start":57,"column_end":73,"is_primary":true,"text":[{"text":"            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);","highlight_start":57,"highlight_end":73}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:94:38\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\", escaped_lyrics)]);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                      \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            cmd.args(&[\"-metadata\", &format!(\"lyrics={}\"\u001b[0m\u001b[0m\u001b[38;5;9m, escaped_lyrics\u001b[0m\u001b[0m)]);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            cmd.args(&[\"-metadata\", &format!(\"lyrics={\u001b[0m\u001b[0m\u001b[38;5;10mescaped_lyrics\u001b[0m\u001b[0m}\")]);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/file_handler.rs","byte_start":6075,"byte_end":6124,"line_start":188,"line_end":188,"column_start":28,"column_end":77,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":28,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/file_handler.rs","byte_start":6095,"byte_end":6095,"line_start":188,"line_end":188,"column_start":48,"column_end":48,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":48,"highlight_end":48}],"label":null,"suggested_replacement":"stem","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":6098,"byte_end":6098,"line_start":188,"line_end":188,"column_start":51,"column_end":51,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":51,"highlight_end":51}],"label":null,"suggested_replacement":"counter","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":6101,"byte_end":6101,"line_start":188,"line_end":188,"column_start":54,"column_end":54,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":54,"highlight_end":54}],"label":null,"suggested_replacement":"ext","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":6103,"byte_end":6109,"line_start":188,"line_end":188,"column_start":56,"column_end":62,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":56,"highlight_end":62}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":6109,"byte_end":6118,"line_start":188,"line_end":188,"column_start":62,"column_end":71,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":62,"highlight_end":71}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/file_handler.rs","byte_start":6118,"byte_end":6123,"line_start":188,"line_end":188,"column_start":71,"column_end":76,"is_primary":true,"text":[{"text":"            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);","highlight_start":71,"highlight_end":76}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/file_handler.rs:188:28\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            let new_name = format!(\"recovered_{}_{}.{}\", stem, counter, ext);\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            let new_name = format!(\"recovered_{}_{}.{}\"\u001b[0m\u001b[0m\u001b[38;5;9m, stem, counter, ext\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m188\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            let new_name = format!(\"recovered_{\u001b[0m\u001b[0m\u001b[38;5;10mstem\u001b[0m\u001b[0m}_{\u001b[0m\u001b[0m\u001b[38;5;10mcounter\u001b[0m\u001b[0m}.{\u001b[0m\u001b[0m\u001b[38;5;10mext\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/ui.rs","byte_start":2501,"byte_end":2562,"line_start":61,"line_end":61,"column_start":5,"column_end":59,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":5,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":2517,"byte_end":2517,"line_start":61,"line_end":61,"column_start":18,"column_end":18,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":18,"highlight_end":18}],"label":null,"suggested_replacement":"current","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":2520,"byte_end":2520,"line_start":61,"line_end":61,"column_start":21,"column_end":21,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":21,"highlight_end":21}],"label":null,"suggested_replacement":"total","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":2534,"byte_end":2534,"line_start":61,"line_end":61,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":"song_id","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":2536,"byte_end":2545,"line_start":61,"line_end":61,"column_start":33,"column_end":42,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":33,"highlight_end":42}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":2545,"byte_end":2552,"line_start":61,"line_end":61,"column_start":42,"column_end":49,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":42,"highlight_end":49}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":2552,"byte_end":2561,"line_start":61,"line_end":61,"column_start":49,"column_end":58,"is_primary":true,"text":[{"text":"    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)","highlight_start":49,"highlight_end":58}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:61:5\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    format!(\"🎵 [{}/{}] 歌曲ID: {}\", current, total, song_id)\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m     \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    format!(\"🎵 [{}/{}] 歌曲ID: {}\", c\u001b[0m\u001b[0m\u001b[38;5;9murrent, total, song_id)\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m61\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    format!(\"🎵 [{\u001b[0m\u001b[0m\u001b[38;5;10mcurrent\u001b[0m\u001b[0m}/{\u001b[0m\u001b[0m\u001b[38;5;10mtotal\u001b[0m\u001b[0m}] 歌曲ID: {\u001b[0m\u001b[0m\u001b[38;5;10msong_id\u001b[0m\u001b[0m}\")\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/ui.rs","byte_start":3450,"byte_end":3483,"line_start":82,"line_end":82,"column_start":13,"column_end":42,"is_primary":true,"text":[{"text":"            println!(\"   错误: {}\", reason);","highlight_start":13,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/ui.rs","byte_start":3472,"byte_end":3472,"line_start":82,"line_end":82,"column_start":31,"column_end":31,"is_primary":true,"text":[{"text":"            println!(\"   错误: {}\", reason);","highlight_start":31,"highlight_end":31}],"label":null,"suggested_replacement":"reason","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/ui.rs","byte_start":3474,"byte_end":3482,"line_start":82,"line_end":82,"column_start":33,"column_end":41,"is_primary":true,"text":[{"text":"            println!(\"   错误: {}\", reason);","highlight_start":33,"highlight_end":41}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/ui.rs:82:13\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m            println!(\"   错误: {}\", reason);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m            println!(\"   错误: {}\", \u001b[0m\u001b[0m\u001b[38;5;9mreason);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m82\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m            println!(\"   错误: {\u001b[0m\u001b[0m\u001b[38;5;10mreason\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"variables can be used directly in the `format!` string","code":{"code":"clippy::uninlined_format_args","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":3295,"byte_end":3348,"line_start":94,"line_end":94,"column_start":20,"column_end":73,"is_primary":true,"text":[{"text":"    let dir_name = format!(\".browser_data_{}_{}\", process_id, timestamp);","highlight_start":20,"highlight_end":73}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"change this to","code":null,"level":"help","spans":[{"file_name":"src/env_setup.rs","byte_start":3319,"byte_end":3319,"line_start":94,"line_end":94,"column_start":44,"column_end":44,"is_primary":true,"text":[{"text":"    let dir_name = format!(\".browser_data_{}_{}\", process_id, timestamp);","highlight_start":44,"highlight_end":44}],"label":null,"suggested_replacement":"process_id","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/env_setup.rs","byte_start":3322,"byte_end":3322,"line_start":94,"line_end":94,"column_start":47,"column_end":47,"is_primary":true,"text":[{"text":"    let dir_name = format!(\".browser_data_{}_{}\", process_id, timestamp);","highlight_start":47,"highlight_end":47}],"label":null,"suggested_replacement":"timestamp","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/env_setup.rs","byte_start":3324,"byte_end":3336,"line_start":94,"line_end":94,"column_start":49,"column_end":61,"is_primary":true,"text":[{"text":"    let dir_name = format!(\".browser_data_{}_{}\", process_id, timestamp);","highlight_start":49,"highlight_end":61}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/env_setup.rs","byte_start":3336,"byte_end":3347,"line_start":94,"line_end":94,"column_start":61,"column_end":72,"is_primary":true,"text":[{"text":"    let dir_name = format!(\".browser_data_{}_{}\", process_id, timestamp);","highlight_start":61,"highlight_end":72}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: variables can be used directly in the `format!` string\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:94:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let dir_name = format!(\".browser_data_{}_{}\", process_id, timestamp);\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#uninlined_format_args\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: change this to\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m    let dir_name = format!(\".browser_data_{}_{}\"\u001b[0m\u001b[0m\u001b[38;5;9m, process_id, timestamp\u001b[0m\u001b[0m);\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m94\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m    let dir_name = format!(\".browser_data_{\u001b[0m\u001b[0m\u001b[38;5;10mprocess_id\u001b[0m\u001b[0m}_{\u001b[0m\u001b[0m\u001b[38;5;10mtimestamp\u001b[0m\u001b[0m}\");\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"this `map_or` can be simplified","code":{"code":"clippy::unnecessary_map_or","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":6274,"byte_end":6335,"line_start":176,"line_end":176,"column_start":33,"column_end":94,"is_primary":true,"text":[{"text":"     (dir_name.contains(\"_\") || dir_name.chars().last().map_or(false, |c| c.is_ascii_digit())))","highlight_start":33,"highlight_end":94}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or","code":null,"level":"help","spans":[],"children":[],"rendered":null},{"message":"use is_some_and instead","code":null,"level":"help","spans":[{"file_name":"src/env_setup.rs","byte_start":6298,"byte_end":6304,"line_start":176,"line_end":176,"column_start":57,"column_end":63,"is_primary":true,"text":[{"text":"     (dir_name.contains(\"_\") || dir_name.chars().last().map_or(false, |c| c.is_ascii_digit())))","highlight_start":57,"highlight_end":63}],"label":null,"suggested_replacement":"is_some_and","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"src/env_setup.rs","byte_start":6305,"byte_end":6312,"line_start":176,"line_end":176,"column_start":64,"column_end":71,"is_primary":true,"text":[{"text":"     (dir_name.contains(\"_\") || dir_name.chars().last().map_or(false, |c| c.is_ascii_digit())))","highlight_start":64,"highlight_end":71}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: this `map_or` can be simplified\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:176:33\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m     (dir_name.contains(\"_\") || dir_name.chars().last().map_or(false, |c| c.is_ascii_digit())))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                 \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mhelp\u001b[0m\u001b[0m: for further information visit https://rust-lang.github.io/rust-clippy/master/index.html#unnecessary_map_or\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14mhelp\u001b[0m\u001b[0m: use is_some_and instead\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;9m- \u001b[0m\u001b[0m     (dir_name.contains(\"_\") || dir_name.chars().last().\u001b[0m\u001b[0m\u001b[38;5;9mmap_or\u001b[0m\u001b[0m(\u001b[0m\u001b[0m\u001b[38;5;9mfalse, \u001b[0m\u001b[0m|c| c.is_ascii_digit())))\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m176\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[38;5;10m+ \u001b[0m\u001b[0m     (dir_name.contains(\"_\") || dir_name.chars().last().\u001b[0m\u001b[0m\u001b[38;5;10mis_some_and\u001b[0m\u001b[0m(|c| c.is_ascii_digit())))\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":1161,"byte_end":1194,"line_start":35,"line_end":35,"column_start":41,"column_end":74,"is_primary":true,"text":[{"text":"        if let Some(browser_data_dir) = CURRENT_BROWSER_DATA_DIR.as_ref() {","highlight_start":41,"highlight_end":74}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"`#[warn(static_mut_refs)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:35:41\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m35\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m        if let Some(browser_data_dir) = CURRENT_BROWSER_DATA_DIR.as_ref() {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                                         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(static_mut_refs)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":3013,"byte_end":3031,"line_start":86,"line_end":86,"column_start":30,"column_end":48,"is_primary":true,"text":[{"text":"    let music_dir = unsafe { MUSIC_DIR.as_ref().unwrap() };","highlight_start":30,"highlight_end":48}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:86:30\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m86\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let music_dir = unsafe { MUSIC_DIR.as_ref().unwrap() };\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":8877,"byte_end":8895,"line_start":254,"line_end":254,"column_start":14,"column_end":32,"is_primary":true,"text":[{"text":"    unsafe { MUSIC_DIR.as_ref().unwrap() }","highlight_start":14,"highlight_end":32}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:254:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m254\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { MUSIC_DIR.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":8973,"byte_end":8990,"line_start":258,"line_end":258,"column_start":14,"column_end":31,"is_primary":true,"text":[{"text":"    unsafe { TEMP_DIR.as_ref().unwrap() }","highlight_start":14,"highlight_end":31}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:258:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m258\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { TEMP_DIR.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":9076,"byte_end":9101,"line_start":262,"line_end":262,"column_start":14,"column_end":39,"is_primary":true,"text":[{"text":"    unsafe { PROGRESS_MANAGER.as_ref().unwrap() }","highlight_start":14,"highlight_end":39}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:262:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m262\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { PROGRESS_MANAGER.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"creating a shared reference to mutable static","code":{"code":"static_mut_refs","explanation":null},"level":"warning","spans":[{"file_name":"src/env_setup.rs","byte_start":9189,"byte_end":9222,"line_start":266,"line_end":266,"column_start":14,"column_end":47,"is_primary":true,"text":[{"text":"    unsafe { CURRENT_BROWSER_DATA_DIR.as_ref().unwrap() }","highlight_start":14,"highlight_end":47}],"label":"shared reference to mutable static","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: creating a shared reference to mutable static\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/env_setup.rs:266:14\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m266\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    unsafe { CURRENT_BROWSER_DATA_DIR.as_ref().unwrap() }\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m              \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[33mshared reference to mutable static\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: for more information, see <https://doc.rust-lang.org/nightly/edition-guide/rust-2024/static-mut-references.html>\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: shared references to mutable statics are dangerous; it's undefined behavior if the static is mutated or if a mutable reference is created for it while the shared reference lives\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"53 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 53 warnings emitted\u001b[0m\n\n"}
